using System;
using System.Collections.Generic;
using Building;
using UnityEngine;
using Debug = UnityEngine.Debug;

public class AvatarBuildBehavior : MonoBehaviour
{
    public GameObject Structure;
    public Material PreviewMaterial;

    private GameObject previewInstance;
    private MeshFilter previewMeshFilter;
    private Vector3 structureWorldSize;
    private Quaternion currentRotation = Quaternion.identity;
    private Color previewOriginalColor;

    private float maxBuildDistance = 50f;
    private float surfaceOffset = 0.05f;
    private float gridSize = 1f;

    private Vector3 previewTargetPosition;
    private Quaternion previewTargetRotation;

    [SerializeField] private float positionSmoothSpeed = 20f;
    [SerializeField] private float rotationSmoothSpeed = 10f;

    private static readonly Collider[] colliderBuffer = new Collider[32];
    private static readonly RaycastHit[] raycastBuffer = new RaycastHit[32];

    private float structureGridSize
    {
        get
        {
            return 3;
        }
    }

    void Start()
    {
        SpawnPreview();
    }

    void Update()
    {
        UpdatePreviewTarget();
        SmoothUpdatePreviewTransform();
    }

    public void TryBuild()
    {
        var result = GetHitPosition();
        if (result.success)
        {
            if (IsOccupied())
            {
                Debug.Log("Cannot build: space is already occupied.");
                return;
            }

            var builtStructure = Instantiate(Structure, result.position, currentRotation);
            HandleWall(builtStructure);
            var bounds = CalculateWorldBounds(builtStructure);
            AstarPath.active.UpdateGraphs(bounds);
        }
        else
        {
            Debug.Log("Build failed: no surface in range.");
        }
    }

    private void HandleWall(GameObject structure)
    {
        WallConnectionHelper.HandleWallConnections(structure, gridSize, colliderBuffer, LayerHelper.GroundLayerBitMask);
    }

    public void RotateLeft()
    {
        currentRotation *= Quaternion.Euler(0f, -90f, 0f);
        previewTargetRotation = currentRotation;
    }

    public void RotateRight()
    {
        currentRotation *= Quaternion.Euler(0f, 90f, 0f);
        previewTargetRotation = currentRotation;
    }

    private void SpawnPreview()
    {
        var originalMeshFilter = Structure.GetComponentInChildren<MeshFilter>();
        var originalRenderer = Structure.GetComponentInChildren<Renderer>();
    
        if (originalMeshFilter == null || originalRenderer == null)
        {
            Debug.LogError("Structure is missing MeshFilter or Renderer.");
            return;
        }
    
        previewInstance = new GameObject("StructurePreview");
    
        var meshFilter = previewInstance.AddComponent<MeshFilter>();
        previewMeshFilter = meshFilter;
        var meshRenderer = previewInstance.AddComponent<MeshRenderer>();
    
        meshFilter.sharedMesh = originalMeshFilter.sharedMesh;
    
        var uniqueMaterial = new Material(PreviewMaterial);
        meshRenderer.sharedMaterial = uniqueMaterial;
        previewOriginalColor = uniqueMaterial.color;
    
        previewInstance.transform.localScale = originalMeshFilter.transform.lossyScale;
        previewInstance.transform.rotation = currentRotation;
    
        SetLayerRecursively(previewInstance, LayerHelper.NonInteractiveLayer);
        structureWorldSize = originalRenderer.bounds.size;
    
        previewTargetRotation = currentRotation;
    }

    private void UpdatePreviewTarget()
    {
        var result = GetHitPosition();
        if (result.success)
        {
            if (previewInstance != null)
            {
                previewInstance.SetActive(true);
                previewTargetPosition = result.position;
                previewTargetRotation = currentRotation;

                bool occupied = IsOccupied();
                var rend = previewInstance.GetComponent<MeshRenderer>();
                if (rend != null)
                {
                    rend.material.color = occupied ? new Color(1f, 0f, 0f, 0.5f) : previewOriginalColor;
                }
            }
        }
        else
        {
            if (previewInstance != null)
            {
                previewInstance.SetActive(false);
            }
        }
    }

    private void SmoothUpdatePreviewTransform()
    {
        if (previewInstance == null || !previewInstance.activeSelf)
            return;

        previewInstance.transform.position = Vector3.Lerp(
            previewInstance.transform.position,
            previewTargetPosition,
            Time.deltaTime * positionSmoothSpeed
        );

        previewInstance.transform.rotation = Quaternion.Slerp(
            previewInstance.transform.rotation,
            previewTargetRotation,
            Time.deltaTime * rotationSmoothSpeed
        );
    }
    
    private bool IsOccupied()
    {
        if (previewInstance == null) return false;

        var mesh = previewMeshFilter.sharedMesh;
        var localBounds = mesh.bounds;

        Vector3 scaledExtents = Vector3.Scale(localBounds.extents, previewInstance.transform.lossyScale);
        Vector3 worldCenter = previewTargetPosition + previewTargetRotation * localBounds.center;
        Quaternion rotation = previewTargetRotation;

        float margin = 0.05f;
        Vector3 halfExtents = scaledExtents - Vector3.one * margin;

        int hitCount = Physics.OverlapBoxNonAlloc(
            worldCenter,
            halfExtents,
            colliderBuffer,
            rotation,
            LayerHelper.GroundLayerBitMask,
            QueryTriggerInteraction.Ignore
        );
        
        for (int i = 0; i < hitCount; i++)
        {
            var col = colliderBuffer[i];
            if (col.GetComponent<Structure>() != null)
            {
                return true;
            }
        }

        return false;
    }
    
    private (bool success, Vector3 position) GetHitPosition()
    {
        var buildCamera = PlayerCameraManager.Instance.PlayerBuildCamera.transform;
        var ray = new Ray(buildCamera.position, buildCamera.forward);

        var hitCount = Physics.RaycastNonAlloc(ray, raycastBuffer, maxBuildDistance, LayerHelper.GroundLayerBitMask);
        if (hitCount == 0)
            return (false, Vector3.zero);

        Array.Sort(raycastBuffer, 0, hitCount, RaycastHitDistanceComparer.Instance);

        for (var i = 0; i < hitCount; i++)
        {
            var hit = raycastBuffer[i];

            if (hit.collider.GetComponent<Structure>() != null)
                continue;

            var rotatedSize = GetRotatedSize(structureWorldSize, currentRotation);
            var verticalOffset = rotatedSize.y / 2f + surfaceOffset;
            var buildPosition = SnapToGrid(hit.point + Vector3.up * verticalOffset, rotatedSize);
            return (true, buildPosition);
        }

        return (false, Vector3.zero);
    }

    // Can use if I ever want to have minecraft like building
    private (bool success, Vector3 position) GetHitPositionBuildingBlocks()
    {
        var buildCamera = PlayerCameraManager.Instance.PlayerBuildCamera.transform;
        var ray = new Ray(buildCamera.position, buildCamera.forward);
        RaycastHit hit;
    
        if (Physics.Raycast(ray, out hit, maxBuildDistance, LayerHelper.GroundLayerBitMask))
        {
            var normal = hit.normal;
            Vector3 rotatedSize = GetRotatedSize(structureWorldSize, currentRotation);
            var halfSize = rotatedSize / 2f;
            var directionalOffset = new Vector3(
                Mathf.Abs(normal.x) * halfSize.x,
                Mathf.Abs(normal.y) * halfSize.y,
                Mathf.Abs(normal.z) * halfSize.z
            );
    
            var offsetHitPoint = hit.point + normal * (directionalOffset.magnitude + surfaceOffset);
            var buildPosition = SnapToGrid(offsetHitPoint, rotatedSize);
            return (true, buildPosition);
        }
        else
        {
            return (false, Vector3.zero);
        }
    }

    private Vector3 SnapToGrid(Vector3 position, Vector3 rotatedSize)
    {
        Vector3 offset = new Vector3(
            (Mathf.RoundToInt(rotatedSize.x) % 2 == 0) ? structureGridSize / 2f : 0f,
            (Mathf.RoundToInt(rotatedSize.y) % 2 == 0) ? structureGridSize / 2f : 0f,
            (Mathf.RoundToInt(rotatedSize.z) % 2 == 0) ? structureGridSize / 2f : 0f
        );

        return new Vector3(
            Mathf.Round((position.x - offset.x) / structureGridSize) * structureGridSize + offset.x,
            Mathf.Round((position.y - offset.y) / structureGridSize) * structureGridSize + offset.y,
            Mathf.Round((position.z - offset.z) / structureGridSize) * structureGridSize + offset.z
        );
    }

    private Vector3 GetRotatedSize(Vector3 originalSize, Quaternion rotation)
    {
        var right = rotation * Vector3.right * originalSize.x;
        var up = rotation * Vector3.up * originalSize.y;
        var forward = rotation * Vector3.forward * originalSize.z;

        return new Vector3(
            Mathf.Abs(right.x) + Mathf.Abs(up.x) + Mathf.Abs(forward.x),
            Mathf.Abs(right.y) + Mathf.Abs(up.y) + Mathf.Abs(forward.y),
            Mathf.Abs(right.z) + Mathf.Abs(up.z) + Mathf.Abs(forward.z)
        );
    }

    private void SetLayerRecursively(GameObject obj, int layer)
    {
        obj.layer = layer;
        foreach (Transform child in obj.transform)
        {
            SetLayerRecursively(child.gameObject, layer);
        }
    }

    private Bounds CalculateWorldBounds(GameObject obj)
    {
        var renderers = obj.GetComponentsInChildren<Renderer>();
        if (renderers.Length == 0)
            return new Bounds(obj.transform.position, Vector3.one);

        var bounds = renderers[0].bounds;
        for (int i = 1; i < renderers.Length; i++)
        {
            bounds.Encapsulate(renderers[i].bounds);
        }
        return bounds;
    }

    private void OnDestroy()
    {
        if (previewInstance != null)
        {
            Destroy(previewInstance);
        }
    }
    
    private class RaycastHitDistanceComparer : IComparer<RaycastHit>
    {
        public static readonly RaycastHitDistanceComparer Instance = new();
        public int Compare(RaycastHit a, RaycastHit b)
        {
            return a.distance.CompareTo(b.distance);
        }
    }
}
